name: Debug

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        variant: [normalDebug] # matrix with only one item for now

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21

      - name: <PERSON> execute permission to gradlew
        run: chmod +x gradlew

      - name: Build APK
        run: ./gradlew assemble${{ matrix.variant }}

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: app-debug.apk-${{ github.run_number }}
          path: app/build/outputs/apk/normal/debug/*.apk
