<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="TextViewNormal" parent="">
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewNormalCompress" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewHeadline4" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewHeadline4.Compress" parent="TextAppearance.MaterialComponents.Headline4">
        <item name="fontFamily">@font/sans</item>
        <item name="android:textSize">32sp</item>
    </style>

    <style name="TextViewHeadline5" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewCaption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewHeadline6" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewHeadline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewHeadline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewSubtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewSubtitle2" parent="TextAppearance.MaterialComponents.Subtitle2">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewBody1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewButton" parent="TextAppearance.MaterialComponents.Button">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewBody2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="TextViewOverline" parent="TextAppearance.MaterialComponents.Overline">
        <item name="fontFamily">@font/sans</item>
    </style>

    <style name="AppTextAppearance.MaterialAlertDialog.Button" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textAppearance">@style/TextViewButton</item>
        <item name="fontFamily">@font/sans</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">0dp</item>
    </style>

    <style name="AppTextAppearance.MaterialAlertDialog.Body" parent="MaterialAlertDialog.MaterialComponents.Body.Text">
        <item name="android:textAppearance">@style/TextViewBody1</item>
        <item name="fontFamily">@font/sans</item>
        <item name="android:paddingTop">16dp</item>
    </style>

    <style name="AppTextAppearance.MaterialAlertDialog.Title" parent="MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:textAppearance">@style/TextViewHeadline6</item>
        <item name="android:textStyle">bold</item>
        <item name="fontFamily">@font/sans</item>
        <item name="android:padding">16dp</item>
    </style>

    <style name="ToolbarTextAppearanceNormal">
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/TextViewHeadline6</item>
        <item name="fontFamily">@font/sans</item>
        <item name="android:textSize">20sp</item>
        <item name="android:letterSpacing">0.0125</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="circleImageView" parent="ShapeAppearance.MaterialComponents">
        <item name="cornerSize">40dp</item>
    </style>

    <style name="BottomSheetItemTextAppearance" parent="Widget.MaterialComponents.BottomNavigationView.Colored">
        <item name="android:textSize">13sp</item>
        <item name="fontFamily">@font/sans</item>
    </style>
</resources>