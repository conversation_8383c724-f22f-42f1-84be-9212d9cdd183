name: Release

on:
  workflow_dispatch: 

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        variant: [Release]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: 21

      - name: <PERSON> execute permission to gradlew
        run: chmod +x gradlew

      - name: Build Release APK
        run: ./gradlew assemble${{ matrix.variant }}
      
      - uses: ilharp/sign-android-release@v2
        name: Sign Normal APK
        id: sign_app
        with:
          releaseDir: app/build/outputs/apk/normal/release
          signingKey: ${{ secrets.ANDROID_SIGNING_KEY }}
          keyAlias: ${{ secrets.ANDROID_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.ANDROID_KEY_PASSWORD }}
          buildToolsVersion: 36.0.0

      - uses: ilharp/sign-android-release@v2
        name: Sign F-Droid APK
        id: sign_fdroid
        with:
          releaseDir: app/build/outputs/apk/fdroid/release
          signingKey: ${{ secrets.ANDROID_SIGNING_KEY }}
          keyAlias: ${{ secrets.ANDROID_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.ANDROID_KEY_PASSWORD }}
          buildToolsVersion: 36.0.0

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: app-signed-release.apk-${{ github.run_number }}
          path: |
            ${{steps.sign_app.outputs.signedFile}}
            ${{steps.sign_fdroid.outputs.signedFile}}
